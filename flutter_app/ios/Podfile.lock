PODS:
  - Flutter (1.0.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"

SPEC CHECKSUMS:
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a

PODFILE CHECKSUM: 10eb9233a8defd56b3e4e3f7681b53aa9436ae12

COCOAPODS: 1.16.2
